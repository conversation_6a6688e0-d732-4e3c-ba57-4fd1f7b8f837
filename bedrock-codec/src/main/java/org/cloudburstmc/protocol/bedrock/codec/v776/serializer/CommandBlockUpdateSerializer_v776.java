package org.cloudburstmc.protocol.bedrock.codec.v776.serializer;

import io.netty.buffer.ByteBuf;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.cloudburstmc.protocol.bedrock.codec.BedrockCodecHelper;
import org.cloudburstmc.protocol.bedrock.codec.BedrockPacketSerializer;
import org.cloudburstmc.protocol.bedrock.codec.v361.serializer.CommandBlockUpdateSerializer_v361;
import org.cloudburstmc.protocol.bedrock.data.CommandBlockMode;
import org.cloudburstmc.protocol.bedrock.packet.CommandBlockUpdatePacket;
import org.cloudburstmc.protocol.common.util.VarInts;

@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class CommandBlockUpdateSerializer_v776 extends CommandBlockUpdateSerializer_v361 {
    public static final CommandBlockUpdateSerializer_v776 INSTANCE = new CommandBlockUpdateSerializer_v776();

    @Override
    public void serialize(ByteBuf buffer, BedrockCodec<PERSON>elper helper, CommandBlockUpdatePacket packet) {
        buffer.writeBoolean(packet.isBlock());

        if (packet.isBlock()) {
            helper.writeBlockPosition(buffer, packet.getBlockPosition());
            VarInts.writeUnsignedInt(buffer, packet.getMode().ordinal());
            buffer.writeBoolean(packet.isRedstoneMode());
            buffer.writeBoolean(packet.isConditional());
        } else {
            VarInts.writeUnsignedLong(buffer, packet.getMinecartRuntimeEntityId());
        }

        helper.writeString(buffer, packet.getCommand());
        helper.writeString(buffer, packet.getLastOutput());
        helper.writeString(buffer, packet.getName());
        helper.writeString(buffer, packet.getFilteredName());
        buffer.writeBoolean(packet.isOutputTracked());
        buffer.writeIntLE((int) packet.getTickDelay());
        buffer.writeBoolean(packet.isExecutingOnFirstTick());
    }

    @Override
    public void deserialize(ByteBuf buffer, BedrockCodecHelper helper, CommandBlockUpdatePacket packet) {
        packet.setBlock(buffer.readBoolean());

        if (packet.isBlock()) {
            packet.setBlockPosition(helper.readBlockPosition(buffer));
            packet.setMode(CommandBlockMode.values()[VarInts.readUnsignedInt(buffer)]);
            packet.setRedstoneMode(buffer.readBoolean());
            packet.setConditional(buffer.readBoolean());
        } else {
            packet.setMinecartRuntimeEntityId(VarInts.readUnsignedLong(buffer));
        }

        packet.setCommand(helper.readString(buffer));
        packet.setLastOutput(helper.readString(buffer));
        packet.setName(helper.readString(buffer));
        packet.setFilteredName(helper.readString(buffer));
        packet.setOutputTracked(buffer.readBoolean());
        packet.setTickDelay(buffer.readUnsignedIntLE());
        packet.setExecutingOnFirstTick(buffer.readBoolean());
    }
}
