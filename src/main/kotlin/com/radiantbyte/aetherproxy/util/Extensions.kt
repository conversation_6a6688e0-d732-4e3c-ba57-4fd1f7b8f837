package com.radiantbyte.aetherproxy.util

import com.radiantbyte.aetherproxy.event.PacketEvent
import com.radiantbyte.aetherproxy.event.AetherEvent
import com.radiantbyte.aetherproxy.event.handler.EventHandler
import com.radiantbyte.aetherproxy.module.ModuleManager
import com.radiantbyte.aetherproxy.module.combat.KillauraModule
import com.radiantbyte.aetherproxy.module.combat.TargetModule
import com.radiantbyte.aetherproxy.module.combat.AntiKnockbackModule
import com.radiantbyte.aetherproxy.module.misc.ShowPositionModule
import com.radiantbyte.aetherproxy.module.misc.DesyncModule
import com.radiantbyte.aetherproxy.module.misc.AntiAFKModule
import com.radiantbyte.aetherproxy.module.misc.ChestTrackerModule
import com.radiantbyte.aetherproxy.module.misc.WaypointsModule
import com.radiantbyte.aetherproxy.module.motion.FlyModule
import com.radiantbyte.aetherproxy.module.motion.AutoSprintModule
import com.radiantbyte.aetherproxy.module.motion.SpiderModule

import com.radiantbyte.aetherproxy.module.motion.HighJumpModule
import com.radiantbyte.aetherproxy.module.motion.MotionFlyModule
import com.radiantbyte.aetherproxy.module.motion.AutoWalkModule
import com.radiantbyte.aetherproxy.module.motion.BHopModule
import com.radiantbyte.aetherproxy.module.motion.JetpackModule
import com.radiantbyte.aetherproxy.module.effect.NightVisionModule
import com.radiantbyte.aetherproxy.module.effect.HasteModule
import com.radiantbyte.aetherproxy.module.visual.ZoomModule
import com.radiantbyte.aetherproxy.module.visual.FreecamModule
import com.radiantbyte.aetherproxy.module.combat.HitboxModule
import com.radiantbyte.aetherproxy.session.EventReceiver
import com.radiantbyte.aetherproxy.session.EventUnregister
import com.radiantbyte.aetherproxy.session.AetherSession
import com.google.gson.GsonBuilder
import com.google.gson.JsonParser
import net.raphimc.minecraftauth.MinecraftAuth
import net.raphimc.minecraftauth.step.bedrock.session.StepFullBedrockSession
import net.raphimc.minecraftauth.step.msa.StepMsaDeviceCode
import org.cloudburstmc.protocol.bedrock.data.command.CommandData
import org.cloudburstmc.protocol.bedrock.data.command.CommandPermission
import org.cloudburstmc.protocol.bedrock.packet.AvailableCommandsPacket
import org.cloudburstmc.protocol.bedrock.packet.BedrockPacket
import org.cloudburstmc.protocol.bedrock.packet.CommandRequestPacket
import org.cloudburstmc.protocol.bedrock.packet.TextPacket
import java.io.File
import java.nio.file.Paths

fun AetherSession.on(eventReceiver: EventReceiver): EventUnregister {
    eventReceivers.add(eventReceiver)
    return {
        eventReceivers.remove(eventReceiver)
    }
}

inline fun <reified T : AetherEvent> AetherSession.event(
    crossinline eventReceiver: AetherSession.(T, EventUnregister) -> Unit
): EventUnregister {
    return on { event, eventUnregister ->
        if (event is T) {
            eventReceiver(event, eventUnregister)
        }
    }
}

@Suppress("UNCHECKED_CAST")
inline fun <reified T : BedrockPacket> AetherSession.packet(
    crossinline eventReceiver: AetherSession.(PacketEvent<T>, EventUnregister) -> Unit
): EventUnregister {
    return on { event, eventUnregister ->
        if (event is PacketEvent<*> && event.packet is T) {
            eventReceiver(event as PacketEvent<T>, eventUnregister)
        }
    }
}

private val gson = GsonBuilder()
    .setPrettyPrinting()
    .create()

fun saveAccount(
    fullBedrockSession: StepFullBedrockSession.FullBedrockSession,
    file: File? = Paths.get(".").resolve("bedrockSession.json").toFile()
) {
    if (file != null && !file.isDirectory) {
        val json = gson.toJson(MinecraftAuth.BEDROCK_DEVICE_CODE_LOGIN.toJson(fullBedrockSession))
        file.writeText(json)
    }
}

fun fetchAccount(
    cache: Boolean = true,
    file: File? = Paths.get(".").resolve("bedrockSession.json").toFile(),
    msaDeviceCodeCallback: StepMsaDeviceCode.MsaDeviceCodeCallback = StepMsaDeviceCode.MsaDeviceCodeCallback {
        println("Go to ${it.directVerificationUri}")
    }
): StepFullBedrockSession.FullBedrockSession {
    if (cache && file != null && file.exists()) {
        val json = JsonParser.parseString(file.readText()).asJsonObject
        return MinecraftAuth.BEDROCK_DEVICE_CODE_LOGIN.fromJson(json)
    }

    val fullBedrockSession = MinecraftAuth.BEDROCK_DEVICE_CODE_LOGIN.getFromInput(
        MinecraftAuth.createHttpClient(),
        msaDeviceCodeCallback
    )

    if (cache) {
        saveAccount(fullBedrockSession, file)
    }

    println("Username: ${fullBedrockSession.mcChain.displayName}")
    println("Expired: ${fullBedrockSession.isExpired}")
    return fullBedrockSession
}

fun StepFullBedrockSession.FullBedrockSession.refresh(): StepFullBedrockSession.FullBedrockSession {
    return MinecraftAuth.BEDROCK_DEVICE_CODE_LOGIN.refresh(MinecraftAuth.createHttpClient(), this)
}

inline fun AetherSession.command(
    commandName: String,
    commandDescription: String = commandName,
    crossinline handler: AetherSession.(List<String>) -> Unit,
    vararg helps: Array<String>
): Pair<EventUnregister, EventUnregister> {
    return packet<AvailableCommandsPacket> { packetEvent, _ ->
        val packet = packetEvent.packet
        packet.commands.add(
            CommandData(
                commandName,
                commandDescription,
                emptySet(),
                CommandPermission.ANY,
                null,
                emptyList(),
                arrayOf(),
            )
        )
    } to packet<CommandRequestPacket> { packetEvent, _ ->
        val packet = packetEvent.packet
        val command = packet.command
        val arguments = "\\s+".toRegex().split(command.trim())

        if (arguments[0] == "/$commandName") {
            packetEvent.consume()
            val subList = arguments.subList(1, arguments.size)
            runCatching {
                handler(subList)
            }.exceptionOrNull()?.let { exception ->
                inbound(TextPacket().apply {
                    type = TextPacket.Type.RAW
                    isNeedsTranslation = false
                    sourceName = ""
                    message = """
                        |§cParse or execute command failed: 
                        |Input: /${commandName}${subList.joinToString(prefix = " ", separator = " ")}
                        |Helps: ${helps.joinToString(separator = "\n") { "/" + commandName + " " + it.joinToString(" ") }}
                        |Error: ${exception.message}
                    """.trimMargin()
                    xuid = ""
                })
            }
        }
    }
}

inline fun EventHandler.command(
    commandName: String,
    commandDescription: String = commandName,
    crossinline handler: AetherSession.(List<String>) -> Unit,
    vararg helps: Array<String>,
): Pair<EventUnregister, EventUnregister> {
    return aetherSession.command(commandName, commandDescription, handler, *helps)
}

fun mismatch(): Nothing = error("Mismatched argument(s)")

fun ModuleManager.installAllModules() {
    // Motion modules
    register(FlyModule(aetherSession))
    register(AutoSprintModule(aetherSession))
    register(SpiderModule(aetherSession))

    register(HighJumpModule(aetherSession))
    register(MotionFlyModule(aetherSession))
    register(AutoWalkModule(aetherSession))
    register(BHopModule(aetherSession))
    register(JetpackModule(aetherSession))

    // Combat modules
    register(KillauraModule(aetherSession))
    register(TargetModule(aetherSession))
    register(AntiKnockbackModule(aetherSession))
    register(HitboxModule(aetherSession))

    // Effect modules
    register(NightVisionModule(aetherSession))
    register(HasteModule(aetherSession))

    // Visual modules
    register(ZoomModule(aetherSession))
    register(FreecamModule(aetherSession))

    // Misc modules
    register(ShowPositionModule(aetherSession))
    register(DesyncModule(aetherSession))
    register(AntiAFKModule(aetherSession))
    register(ChestTrackerModule(aetherSession))
    register(WaypointsModule(aetherSession))
}
