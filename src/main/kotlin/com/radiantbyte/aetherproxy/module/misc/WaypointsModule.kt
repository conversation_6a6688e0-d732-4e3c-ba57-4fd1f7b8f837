package com.radiantbyte.aetherproxy.module.misc

import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import org.cloudburstmc.math.vector.Vector3f
import org.cloudburstmc.math.vector.Vector3i
import org.cloudburstmc.protocol.bedrock.packet.PlayerAuthInputPacket
import org.cloudburstmc.protocol.bedrock.packet.TextPacket
import kotlin.math.sqrt

class WaypointsModule(aetherSession: AetherSession) : Module(aetherSession, "Waypoints", ModuleCategory.Misc) {

    var showDistance by boolValue("ShowDistance", true)
    var maxWaypoints by intValue("MaxWaypoints", 10, 1..20)

    private val waypoints = mutableMapOf<Int, Waypoint>()
    private var lastDisplayTime = 0L

    data class Waypoint(
        val id: Int,
        val position: Vector3i,
        val message: String,
        val timestamp: Long = System.currentTimeMillis()
    )

    init {
        command(
            "waypoints",
            "Manage waypoints",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    1 -> {
                        when (arguments[0]) {
                            "list" -> listWaypoints()
                            "clear" -> clearWaypoints()
                            else -> mismatch()
                        }
                    }
                    2 -> {
                        when (arguments[0]) {
                            "distance" -> {
                                showDistance = arguments[1].toBoolean()
                                displayClientMessage("Distance display: $showDistance")
                            }
                            "max" -> {
                                maxWaypoints = arguments[1].toInt()
                                displayClientMessage("Max waypoints set to $maxWaypoints")
                            }
                            "remove" -> {
                                val id = arguments[1].toIntOrNull()
                                if (id != null) {
                                    removeWaypoint(id)
                                } else {
                                    displayClientMessage("Invalid waypoint ID")
                                }
                            }
                            else -> mismatch()
                        }
                    }
                    else -> mismatch()
                }
            }
        )

        packet<TextPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            if (packet.type == TextPacket.Type.CHAT && packet.sourceName == aetherSession.localPlayer.username) {
                val message = packet.message
                if (message.startsWith("/mark ")) {
                    packetEvent.consume()
                    handleMarkCommand(message.substring(6))
                }
            }
        }

        packet<PlayerAuthInputPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val currentTime = System.currentTimeMillis()
            if (currentTime - lastDisplayTime > 2000) {
                updateWaypointDisplay()
                lastDisplayTime = currentTime
            }
        }
    }

    private fun handleMarkCommand(args: String) {
        val parts = args.split(" ", limit = 2)
        val idStr = parts[0]
        val message = if (parts.size > 1) parts[1] else "Waypoint"

        val id = idStr.toIntOrNull()
        if (id == null || id < 1 || id > 10) {
            displayClientMessage("Usage: /mark <1-10> [message]")
            return
        }

        val playerPos = aetherSession.localPlayer.position
        val waypointPos = Vector3i.from(playerPos.x.toInt(), playerPos.y.toInt(), playerPos.z.toInt())
        
        val waypoint = Waypoint(id, waypointPos, message)
        waypoints[id] = waypoint
        
        displayClientMessage("Waypoint $id saved: $message at $waypointPos")
    }

    private fun listWaypoints() {
        if (waypoints.isEmpty()) {
            displayClientMessage("No waypoints saved")
            return
        }

        displayClientMessage("Saved waypoints:")
        waypoints.values.sortedBy { it.id }.forEach { waypoint ->
            val distance = if (showDistance) {
                val dist = calculateDistance(aetherSession.localPlayer.position, waypoint.position)
                " (${dist.toInt()}m)"
            } else ""
            
            displayClientMessage("${waypoint.id}: ${waypoint.message} at ${waypoint.position}$distance")
        }
    }

    private fun clearWaypoints() {
        waypoints.clear()
        displayClientMessage("All waypoints cleared")
    }

    private fun removeWaypoint(id: Int) {
        if (waypoints.remove(id) != null) {
            displayClientMessage("Waypoint $id removed")
        } else {
            displayClientMessage("Waypoint $id not found")
        }
    }

    private fun updateWaypointDisplay() {
        if (waypoints.isEmpty()) {
            setTitle("")
            return
        }

        val playerPos = aetherSession.localPlayer.position
        val nearestWaypoints = waypoints.values.sortedBy { waypoint ->
            calculateDistance(playerPos, waypoint.position)
        }.take(3)

        val displayText = if (showDistance) {
            nearestWaypoints.joinToString(" | ") { waypoint ->
                val distance = calculateDistance(playerPos, waypoint.position).toInt()
                "${waypoint.id}:${waypoint.message} (${distance}m)"
            }
        } else {
            nearestWaypoints.joinToString(" | ") { waypoint ->
                "${waypoint.id}:${waypoint.message}"
            }
        }

        setTitle("Waypoints [${waypoints.size}]: $displayText")
    }

    private fun calculateDistance(pos1: Vector3f, pos2: Vector3i): Float {
        val dx = pos1.x - pos2.x
        val dy = pos1.y - pos2.y
        val dz = pos1.z - pos2.z
        return sqrt(dx * dx + dy * dy + dz * dz)
    }
}
