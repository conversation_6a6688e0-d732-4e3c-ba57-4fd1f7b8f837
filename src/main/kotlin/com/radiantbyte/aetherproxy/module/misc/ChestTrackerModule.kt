package com.radiantbyte.aetherproxy.module.misc

import com.radiantbyte.aetherproxy.config.ListItem
import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import org.cloudburstmc.math.vector.Vector3i
import org.cloudburstmc.protocol.bedrock.data.definitions.BlockDefinition
import org.cloudburstmc.protocol.bedrock.packet.*
import kotlin.math.sqrt

class ChestTrackerModule(aetherSession: AetherSession) : Module(aetherSession, "ChestTracker", ModuleCategory.Misc) {

    var range by listValue("Range", TrackingRange.Nearby, TrackingRange.entries.toTypedArray())
    var showDistance by boolValue("ShowDistance", true)
    var maxChests by intValue("MaxChests", 50, 10..200)

    private val trackedChests = mutableSetOf<Vector3i>()
    private var lastDisplayTime = 0L

    init {
        command(
            "chesttracker",
            "Track and display chest locations",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    2 -> {
                        when (arguments[0]) {
                            "range" -> {
                                range = TrackingRange.entries.find { it.itemName.equals(arguments[1], true) }
                                    ?: range
                                displayClientMessage("ChestTracker range set to ${range.itemName}")
                            }
                            "distance" -> {
                                showDistance = arguments[1].toBoolean()
                                displayClientMessage("ChestTracker distance display: $showDistance")
                            }
                            "maxchests" -> {
                                maxChests = arguments[1].toInt()
                                displayClientMessage("ChestTracker max chests set to $maxChests")
                            }
                            "clear" -> {
                                trackedChests.clear()
                                displayClientMessage("Cleared all tracked chests")
                            }
                            "add" -> {
                                val pos = aetherSession.localPlayer.position
                                val chestPos = Vector3i.from(pos.x.toInt(), pos.y.toInt(), pos.z.toInt())
                                trackedChests.add(chestPos)
                                displayClientMessage("Added chest at current position: $chestPos")
                            }
                            "count" -> {
                                displayClientMessage("Tracked chests: ${trackedChests.size}")
                            }
                            else -> mismatch()
                        }
                    }
                    else -> mismatch()
                }
            }
        )

        packet<UpdateBlockPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            if (isChestBlock(packet.definition)) {
                trackedChests.add(packet.blockPosition)
                if (trackedChests.size > maxChests) {
                    val oldest = trackedChests.first()
                    trackedChests.remove(oldest)
                }
                displayClientMessage("Chest detected at ${packet.blockPosition}")
            }
        }

        packet<ContainerOpenPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            if (isChestContainer(packet.type)) {
                trackedChests.add(packet.blockPosition)
                displayClientMessage("Chest found at ${packet.blockPosition}")
            }
        }

        packet<UpdateSubChunkBlocksPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            packet.standardBlocks.forEach { blockChange ->
                if (isChestBlock(blockChange.definition)) {
                    val worldPos = Vector3i.from(
                        packet.chunkX * 16 + blockChange.position.x,
                        blockChange.position.y,
                        packet.chunkZ * 16 + blockChange.position.z
                    )
                    trackedChests.add(worldPos)
                    if (trackedChests.size > maxChests) {
                        val oldest = trackedChests.first()
                        trackedChests.remove(oldest)
                    }
                }
            }
        }

        packet<LevelChunkPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }
            
            displayClientMessage("Chunk loaded at ${packetEvent.packet.chunkX}, ${packetEvent.packet.chunkZ}")
        }

        packet<BlockEntityDataPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            val nbtData = packet.data
            if (nbtData.getString("id", "").contains("chest", true)) {
                trackedChests.add(packet.blockPosition)
                if (trackedChests.size > maxChests) {
                    val oldest = trackedChests.first()
                    trackedChests.remove(oldest)
                }
                displayClientMessage("Chest entity at ${packet.blockPosition}")
            }
        }

        packet<PlayerAuthInputPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val currentTime = System.currentTimeMillis()
            if (currentTime - lastDisplayTime > 1000) {
                updateChestDisplay()
                lastDisplayTime = currentTime
            }
        }

        packet<StartGamePacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            trackedChests.clear()
            displayClientMessage("ChestTracker reset for new game")
        }
    }

    private fun isChestBlock(definition: BlockDefinition?): Boolean {
        if (definition == null) return false

        return try {
            val runtimeId = definition.runtimeId
            runtimeId in listOf(54, 146, 130, 154, 205, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232)
        } catch (e: Exception) {
            false
        }
    }

    private fun isChestContainer(type: org.cloudburstmc.protocol.bedrock.data.inventory.ContainerType): Boolean {
        return type == org.cloudburstmc.protocol.bedrock.data.inventory.ContainerType.CONTAINER ||
               type == org.cloudburstmc.protocol.bedrock.data.inventory.ContainerType.MINECART_CHEST ||
               type == org.cloudburstmc.protocol.bedrock.data.inventory.ContainerType.CHEST_BOAT
    }

    private fun updateChestDisplay() {
        if (trackedChests.isEmpty()) {
            setTitle("No chests tracked")
            return
        }

        val playerPos = aetherSession.localPlayer.position
        val nearbyChests = trackedChests.filter { chestPos ->
            val distance = calculateDistance(playerPos, chestPos)
            distance <= range.maxDistance
        }.sortedBy { chestPos ->
            calculateDistance(playerPos, chestPos)
        }.take(10)

        if (nearbyChests.isEmpty()) {
            setTitle("No chests in range")
            return
        }

        val displayText = if (showDistance) {
            nearbyChests.take(3).joinToString(" | ") { chestPos ->
                val distance = calculateDistance(playerPos, chestPos).toInt()
                "${chestPos.x},${chestPos.y},${chestPos.z} (${distance}m)"
            }
        } else {
            nearbyChests.take(5).joinToString(" | ") { chestPos ->
                "${chestPos.x},${chestPos.y},${chestPos.z}"
            }
        }

        setTitle("Chests [${nearbyChests.size}]: $displayText")
    }

    private fun calculateDistance(pos1: org.cloudburstmc.math.vector.Vector3f, pos2: Vector3i): Float {
        val dx = pos1.x - pos2.x
        val dy = pos1.y - pos2.y
        val dz = pos1.z - pos2.z
        return sqrt(dx * dx + dy * dy + dz * dz)
    }

    enum class TrackingRange(override val itemName: String, val maxDistance: Float) : ListItem {
        Nearby("Nearby", 50f),
        Medium("Medium", 100f),
        Wide("Wide", 200f),
        Extreme("Extreme", 500f)
    }
}
