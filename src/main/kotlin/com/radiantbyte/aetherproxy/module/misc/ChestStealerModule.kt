package com.radiantbyte.aetherproxy.module.misc

import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import org.cloudburstmc.math.vector.Vector3i
import org.cloudburstmc.protocol.bedrock.data.inventory.ContainerType
import org.cloudburstmc.protocol.bedrock.data.inventory.ItemData
import org.cloudburstmc.protocol.bedrock.data.inventory.transaction.InventoryActionData
import org.cloudburstmc.protocol.bedrock.data.inventory.transaction.InventorySource
import org.cloudburstmc.protocol.bedrock.data.inventory.transaction.InventoryTransactionType
import org.cloudburstmc.protocol.bedrock.packet.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class ChestStealerModule(aetherSession: AetherSession) : Module(aetherSession, "ChestStealer", ModuleCategory.Misc) {

    var delay by intValue("Delay", 100, 50..1000)
    var maxItemsPerTick by intValue("MaxItemsPerTick", 1, 1..5)

    private var currentChestId: Byte? = null
    private var currentChestPosition: Vector3i? = null
    private var chestContents = mutableListOf<ItemData>()
    private var isProcessing = false

    init {
        command(
            "cheststealer",
            "Automatically steal items from opened chests",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    2 -> {
                        when (arguments[0]) {
                            "delay" -> {
                                delay = arguments[1].toInt()
                                displayClientMessage("ChestStealer delay set to ${delay}ms")
                            }
                            "maxitems" -> {
                                maxItemsPerTick = arguments[1].toInt()
                                displayClientMessage("ChestStealer max items per tick set to $maxItemsPerTick")
                            }
                            else -> mismatch()
                        }
                    }
                    else -> mismatch()
                }
            }
        )

        packet<ContainerOpenPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            if (isChestContainer(packet.type)) {
                currentChestId = packet.id
                currentChestPosition = packet.blockPosition
                chestContents.clear()
                isProcessing = false
                displayClientMessage("Chest opened at ${packet.blockPosition}")
            }
        }

        packet<ContainerClosePacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet
            if (packet.id == currentChestId) {
                currentChestId = null
                currentChestPosition = null
                chestContents.clear()
                isProcessing = false
            }
        }

        packet<InventoryContentPacket> { packetEvent, _ ->
            if (!isEnabled() || currentChestId == null) {
                return@packet
            }

            val packet = packetEvent.packet
            if (packet.containerId == currentChestId?.toInt()) {
                chestContents.clear()
                chestContents.addAll(packet.contents)

                val nonEmptyItems = chestContents.count { it != ItemData.AIR }
                if (nonEmptyItems > 0 && !isProcessing) {
                    displayClientMessage("Found $nonEmptyItems items in chest")
                    startStealing()
                }
            }
        }

        packet<InventorySlotPacket> { packetEvent, _ ->
            if (!isEnabled() || currentChestId == null) {
                return@packet
            }

            val packet = packetEvent.packet
            if (packet.containerId == currentChestId?.toInt()) {
                val slotIndex = packet.slot
                if (packet.item == ItemData.AIR) {
                    if (slotIndex < chestContents.size) {
                        chestContents.removeAt(slotIndex)
                    }
                } else {
                    while (chestContents.size <= slotIndex) {
                        chestContents.add(ItemData.AIR)
                    }
                    chestContents[slotIndex] = packet.item
                }
            }
        }
    }

    private fun isChestContainer(type: ContainerType): Boolean {
        return type == ContainerType.CONTAINER || 
               type == ContainerType.MINECART_CHEST || 
               type == ContainerType.CHEST_BOAT
    }

    private fun startStealing() {
        if (isProcessing || currentChestId == null) return

        isProcessing = true
        coroutineScope.launch {
            while (chestContents.any { it != ItemData.AIR } && isEnabled() && currentChestId != null) {
                var itemsStolen = 0

                for (index in chestContents.indices) {
                    if (itemsStolen >= maxItemsPerTick) break

                    val item = chestContents[index]
                    if (item != ItemData.AIR) {
                        stealItem(index, item)
                        chestContents[index] = ItemData.AIR
                        itemsStolen++
                    }
                }

                if (delay > 0) {
                    delay(delay.toLong())
                }
            }
            isProcessing = false
        }
    }

    private fun stealItem(slotIndex: Int, item: ItemData) {
        val chestId = currentChestId ?: return

        val inventoryTransaction = InventoryTransactionPacket().apply {
            transactionType = InventoryTransactionType.NORMAL
            legacyRequestId = 0

            actions.add(InventoryActionData(
                InventorySource.fromContainerWindowId(chestId.toInt()),
                slotIndex,
                item,
                ItemData.AIR,
                0
            ))

            actions.add(InventoryActionData(
                InventorySource.fromContainerWindowId(0),
                findEmptyInventorySlot(),
                ItemData.AIR,
                item,
                0
            ))
        }

        aetherSession.outbound(inventoryTransaction)
        displayClientMessage("Stealing ${item.definition.identifier} from slot $slotIndex")
    }

    private fun findEmptyInventorySlot(): Int {
        return (9..35).random()
    }
}
