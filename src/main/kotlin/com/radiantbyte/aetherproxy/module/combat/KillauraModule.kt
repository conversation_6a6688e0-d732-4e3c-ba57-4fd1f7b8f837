package com.radiantbyte.aetherproxy.module.combat

import com.radiantbyte.aetherproxy.bedrock.entity.LocalPlayer
import com.radiantbyte.aetherproxy.bedrock.entity.Player
import com.radiantbyte.aetherproxy.config.ListItem
import com.radiantbyte.aetherproxy.config.lookup
import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import org.cloudburstmc.protocol.bedrock.packet.PlayerAuthInputPacket

class KillauraModule(aetherSession: AetherSession) : Module(aetherSession, "Killaura", ModuleCategory.Combat) {

    var mode by listValue("Mode", KillauraMode.Single, KillauraMode.entries.toTypedArray())

    var cps by intValue("CPS", 8, 1..20)

    var radius by floatValue("Radius", 6f, 2f..20f)

    private var lastTickTime = 0L

    private var lastDelay = 0L

    init {
        command(
            "killaura",
            "Auto hit the closest entity or player",
            handler = {
                when (it.size) {
                    0 -> toggle()

                    2 -> {
                        when (it[0]) {

                            "mode" -> {
                                mode = KillauraMode.entries.toTypedArray().lookup(it[1])
                            }

                            "cps" -> {
                                cps = it[1].toInt()
                            }

                            "range" -> {
                                radius = it[1].toFloat()
                            }

                            else -> mismatch()
                        }
                    }

                    else -> mismatch()
                }
            }
        )

        packet<PlayerAuthInputPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            if ((System.currentTimeMillis() - lastTickTime) >= lastDelay) {
                val targetModule = aetherSession.moduleManager.getModule<TargetModule>()

                // Get targets from TargetModule if it exists, otherwise use a safe default
                val targets = if (targetModule != null) {
                    targetModule.targets
                } else {
                    // Fallback: only target players (excluding local player) when TargetModule is not available
                    aetherSession.level.entities.filterIsInstance<Player>()
                        .filter { it !is LocalPlayer }
                }

                val validTargets = targets
                    .filter {
                        it.position.distance(localPlayer.position) <= radius
                    }
                    .sortedBy { it.position.distance(localPlayer.position) }

                if (validTargets.isEmpty()) {
                    return@packet
                }

                val aimTarget = validTargets.first()

                when (mode) {
                    KillauraMode.Single -> {
                        localPlayer.attack(aimTarget)
                        if (aimTarget is Player) {
                            setTitle("Attacking \"${aimTarget.displayName}\"")
                        }
                    }

                    KillauraMode.Multi -> {
                        val attackedPlayers = ArrayList<Player>()

                        validTargets.forEach { entity ->
                            localPlayer.attack(entity)
                            if (entity is Player) {
                                attackedPlayers.add(entity)
                            }
                        }

                        if (attackedPlayers.isNotEmpty()) {
                            setTitle("Attacking [${attackedPlayers.joinToString(separator = ", ") { "\"${it.displayName}\"" }}]")
                        }
                    }
                }

                lastDelay = 1000L / cps
                lastTickTime = System.currentTimeMillis()
            }
        }
    }

    enum class KillauraMode(override val itemName: String) : ListItem {
        Single("Single"),
        Multi("Multi")
    }

}
