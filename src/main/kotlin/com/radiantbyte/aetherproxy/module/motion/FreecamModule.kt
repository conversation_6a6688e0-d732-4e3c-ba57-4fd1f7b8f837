package com.radiantbyte.aetherproxy.module.motion

import com.radiantbyte.aetherproxy.event.handler.packet
import com.radiantbyte.aetherproxy.module.Module
import com.radiantbyte.aetherproxy.module.ModuleCategory
import com.radiantbyte.aetherproxy.session.AetherSession
import com.radiantbyte.aetherproxy.util.command
import com.radiantbyte.aetherproxy.util.mismatch
import org.cloudburstmc.math.vector.Vector3f
import org.cloudburstmc.math.vector.Vector2f
import org.cloudburstmc.protocol.bedrock.data.Ability
import org.cloudburstmc.protocol.bedrock.data.PlayerActionType
import org.cloudburstmc.protocol.bedrock.data.entity.EntityDataTypes
import org.cloudburstmc.protocol.bedrock.packet.*

class FreecamModule(aetherSession: AetherSession) : Module(aetherSession, "Freecam", ModuleCategory.Motion) {

    var speed by floatValue("Speed", 1.0f, 0.1f..5.0f)
    var noClip by boolValue("NoClip", true)

    private var isFreecamActive = false
    private var originalPosition: Vector3f? = null
    private var freecamPosition: Vector3f? = null
    private var freecamRotation: Vector3f? = null

    init {
        command(
            "freecam",
            "Toggle freecam mode",
            handler = { arguments ->
                when (arguments.size) {
                    0 -> toggle()
                    2 -> {
                        when (arguments[0]) {
                            "speed" -> {
                                speed = arguments[1].toFloat()
                                displayClientMessage("Freecam speed set to $speed")
                            }
                            "noclip" -> {
                                noClip = arguments[1].toBoolean()
                                displayClientMessage("Freecam noclip: $noClip")
                            }
                            else -> mismatch()
                        }
                    }
                    else -> mismatch()
                }
            }
        )

        packet<PlayerAuthInputPacket> { packetEvent, _ ->
            if (!isEnabled()) {
                return@packet
            }

            val packet = packetEvent.packet

            if (!isFreecamActive) {
                originalPosition = packet.position
                freecamPosition = packet.position
                freecamRotation = packet.rotation
                isFreecamActive = true
                enableFreecamAbilities()
                displayClientMessage("Freecam activated")
            }

            freecamPosition = packet.position
            freecamRotation = packet.rotation

            packet.position = originalPosition ?: packet.position
            packet.motion = Vector2f.ZERO
        }

        packet<MovePlayerPacket> { packetEvent, _ ->
            if (!isEnabled() || !isFreecamActive) {
                return@packet
            }

            val packet = packetEvent.packet
            packet.position = originalPosition ?: packet.position
            packet.isOnGround = false
        }

        packet<SetEntityDataPacket> { packetEvent, _ ->
            if (!isEnabled() || !isFreecamActive) {
                return@packet
            }

            val packet = packetEvent.packet
            if (packet.runtimeEntityId == aetherSession.localPlayer.runtimeEntityId) {
                if (noClip) {
                    packet.metadata[EntityDataTypes.FLAGS] = 0L
                }
            }
        }

        packet<PlayerActionPacket> { packetEvent, _ ->
            if (!isEnabled() || !isFreecamActive) {
                return@packet
            }

            val packet = packetEvent.packet
            if (packet.action == PlayerActionType.START_FLYING || 
                packet.action == PlayerActionType.STOP_FLYING) {
                packetEvent.consume()
            }
        }
    }

    private fun enableFreecamAbilities() {
        displayClientMessage("Freecam abilities enabled")
    }

    private fun disableFreecamAbilities() {
        displayClientMessage("Freecam abilities disabled")
    }

    fun disableFreecam() {
        if (isFreecamActive) {
            isFreecamActive = false
            disableFreecamAbilities()

            freecamPosition?.let { pos ->
                val teleportPacket = MovePlayerPacket().apply {
                    runtimeEntityId = aetherSession.localPlayer.runtimeEntityId
                    position = pos
                    rotation = freecamRotation ?: Vector3f.ZERO
                    mode = MovePlayerPacket.Mode.TELEPORT
                    isOnGround = true
                    ridingRuntimeEntityId = 0
                    teleportationCause = MovePlayerPacket.TeleportationCause.UNKNOWN
                    entityType = 0
                    tick = 0
                }
                aetherSession.outbound(teleportPacket)
            }

            displayClientMessage("Freecam deactivated")
        }
    }

    fun teleportToFreecamPosition() {
        if (isFreecamActive && freecamPosition != null) {
            originalPosition = freecamPosition
            displayClientMessage("Teleported to freecam position")
        }
    }
}
